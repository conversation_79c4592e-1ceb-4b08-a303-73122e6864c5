LIVE555_PATH = /home/<USER>/3rdparty/bin/gcc-live555/usr/local
INCLUDES = -I$(LIVE555_PATH)/include/liveMedia \
           -I$(LIVE555_PATH)/include/groupsock \
           -I$(LIVE555_PATH)/include/UsageEnvironment \
           -I$(LIVE555_PATH)/include/BasicUsageEnvironment


LIBS = -L$(LIVE555_PATH)/lib \
       -lliveMedia \
       -lgroupsock \
       -lBasicUsageEnvironment \
       -lUsageEnvironment \
       -lpthread

CXX = g++
CXXFLAGS = -std=c++11 -O2 $(INCLUDES)

TARGET = rtsp_ts_streamer
SOURCES = rtsp_ts_streamer.cpp

$(TARGET): $(SOURCES)
	$(CXX) $(CXXFLAGS) -o $(TARGET) $(SOURCES) $(LIBS)

clean:
	rm -f $(TARGET)

.PHONY: clean
