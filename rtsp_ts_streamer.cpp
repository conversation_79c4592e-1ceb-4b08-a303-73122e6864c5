#include <liveMedia.hh>
#include <BasicUsageEnvironment.hh>
#include <GroupsockHelper.hh>
#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include <mutex>
#include <atomic>
#include <vector>
#include <map>

#ifdef _WIN32
    #include <windows.h>
    #include <io.h>
    #define sleep(x) Sleep((x)*1000)
    #define access _access
    #define F_OK 0
#else
    #include <unistd.h>
    #include <signal.h>
#endif

// 前向声明
class RTSPTSStreamer;

class RTSPTSStreamer {
private:
    TaskScheduler* scheduler;
    UsageEnvironment* env;
    RTSPServer* rtspServer;
    ServerMediaSession* sms;
    std::string currentTSFile;
    std::mutex fileMutex;
    std::atomic<bool> isRunning;
    std::atomic<char> shouldStop;  // live555 EventLoopWatchVariable
    int serverPort;
    std::map<std::string, std::string> availableFiles; // streamName -> filePath

public:
    RTSPTSStreamer(int port = 8554);
    ~RTSPTSStreamer();

    bool initialize();
    bool setTSFile(const std::string& tsFilePath);
    bool setTSFileByStreamName(const std::string& streamName);
    void startStreaming();
    void stopStreaming();
    std::string getRTSPURL() const;
    std::string getCurrentTSFile() const;
    bool isFileExists(const std::string& filePath) const;
    void runEventLoop();
};

// 自定义 ServerMediaSubsession 来支持动态文件选择
class DynamicTSServerMediaSubsession : public OnDemandServerMediaSubsession {
public:
    static DynamicTSServerMediaSubsession* createNew(UsageEnvironment& env,
                                                    RTSPTSStreamer* streamer);

protected:
    DynamicTSServerMediaSubsession(UsageEnvironment& env, RTSPTSStreamer* streamer);
    virtual ~DynamicTSServerMediaSubsession();

    virtual FramedSource* createNewStreamSource(unsigned clientSessionId,
                                              unsigned& estBitrate);
    virtual RTPSink* createNewRTPSink(Groupsock* rtpGroupsock,
                                    unsigned char rtpPayloadTypeIfDynamic,
                                    FramedSource* inputSource);

private:
    RTSPTSStreamer* fStreamer;
};

// 实现 DynamicTSServerMediaSubsession
DynamicTSServerMediaSubsession* DynamicTSServerMediaSubsession::createNew(
    UsageEnvironment& env, RTSPTSStreamer* streamer) {
    return new DynamicTSServerMediaSubsession(env, streamer);
}

DynamicTSServerMediaSubsession::DynamicTSServerMediaSubsession(
    UsageEnvironment& env, RTSPTSStreamer* streamer)
    : OnDemandServerMediaSubsession(env, True), fStreamer(streamer) {
}

DynamicTSServerMediaSubsession::~DynamicTSServerMediaSubsession() {
}

FramedSource* DynamicTSServerMediaSubsession::createNewStreamSource(
    unsigned clientSessionId, unsigned& estBitrate) {
    estBitrate = 5000; // 5 Mbps

    envir() << "Creating stream source for client " << clientSessionId << "\n";

    // 获取当前文件路径
    std::string currentFile = fStreamer->getCurrentTSFile();
    if (currentFile.empty()) {
        envir() << "Error: No TS file set\n";
        return NULL;
    }

    envir() << "Using file: " << currentFile.c_str() << "\n";

    // 检查文件是否存在
    if (!fStreamer->isFileExists(currentFile)) {
        envir() << "Error: TS file does not exist: " << currentFile.c_str() << "\n";
        return NULL;
    }

    // 创建文件源
    ByteStreamFileSource* fileSource = ByteStreamFileSource::createNew(envir(), currentFile.c_str());
    if (fileSource == NULL) {
        envir() << "Failed to create ByteStreamFileSource for " << currentFile.c_str() << "\n";
        return NULL;
    }

    // 使用 MPEG2TransportStreamFramer
    MPEG2TransportStreamFramer* framer =
        MPEG2TransportStreamFramer::createNew(envir(), fileSource);
    if (framer == NULL) {
        envir() << "Failed to create MPEG2TransportStreamFramer\n";
        Medium::close(fileSource);
        return NULL;
    }

    envir() << "Successfully created TS stream source\n";
    return framer;
}

RTPSink* DynamicTSServerMediaSubsession::createNewRTPSink(
    Groupsock* rtpGroupsock, unsigned char rtpPayloadTypeIfDynamic,
    FramedSource* inputSource) {

    envir() << "Creating RTP sink for TS stream\n";

    return SimpleRTPSink::createNew(envir(), rtpGroupsock,
                                   33,        // payload type for MP2T
                                   90000,     // timestamp frequency
                                   "video",   // media type
                                   "MP2T",    // RTP format name
                                   1,         // number of channels
                                   False,     // allow multiple frames per packet
                                   False);    // marker bit on last fragment
}

RTSPTSStreamer::RTSPTSStreamer(int port) : serverPort(port), isRunning(false), shouldStop(0) {
    scheduler = BasicTaskScheduler::createNew();
    env = BasicUsageEnvironment::createNew(*scheduler);
    rtspServer = RTSPServer::createNew(*env, port, NULL);
    sms = NULL;

    // 初始化可用文件列表
    availableFiles["video1"] = "/home/<USER>/zeno/rtsp_ts/video.ts";
    availableFiles["video2"] = "/home/<USER>/zeno/rtsp_ts/video2.ts";
    availableFiles["test"] = "/home/<USER>/zeno/rtsp_ts/test.ts";
}

RTSPTSStreamer::~RTSPTSStreamer() {
    stopStreaming();
    Medium::close(rtspServer);
    env->reclaim();
    delete scheduler;
}

bool RTSPTSStreamer::initialize() {
    if (rtspServer == NULL) {
        *env << "Failed to create RTSP server\n";
        return false;
    }
    return true;
}

bool RTSPTSStreamer::setTSFile(const std::string& tsFilePath) {
    std::lock_guard<std::mutex> lock(fileMutex);

    // 检查文件是否存在
    if (!isFileExists(tsFilePath)) {
        std::cerr << "Error: TS file does not exist: " << tsFilePath << std::endl;
        return false;
    }

    stopStreaming();
    currentTSFile = tsFilePath;

    sms = ServerMediaSession::createNew(*env, "stream",
                                       "Dynamic TS Stream", "Session streamed by live555");

    // 使用我们的自定义 subsession
    sms->addSubsession(DynamicTSServerMediaSubsession::createNew(*env, this));
    rtspServer->addServerMediaSession(sms);

    std::cout << "Set TS file: " << currentTSFile << std::endl;
    std::cout << "RTSP URL: " << getRTSPURL() << std::endl;
    return true;
}

bool RTSPTSStreamer::setTSFileByStreamName(const std::string& streamName) {
    std::lock_guard<std::mutex> lock(fileMutex);

    // 查找对应的文件路径
    auto it = availableFiles.find(streamName);
    if (it == availableFiles.end()) {
        std::cerr << "Error: Stream name not found: " << streamName << std::endl;
        return false;
    }

    return setTSFile(it->second);
}

void RTSPTSStreamer::startStreaming() {
    if (sms == NULL) {
        std::cout << "No TS file set. Call setTSFile() first." << std::endl;
        return;
    }

    isRunning = true;
    shouldStop = 0;
    std::cout << "Starting RTSP streaming..." << std::endl;
    std::cout << "Server listening on port " << serverPort << std::endl;
    std::cout << "RTSP URL: " << getRTSPURL() << std::endl;
}

void RTSPTSStreamer::runEventLoop() {
    if (!isRunning) {
        std::cout << "Streamer not started. Call startStreaming() first." << std::endl;
        return;
    }

    // 运行事件循环，但允许中断
    env->taskScheduler().doEventLoop(&shouldStop);
}

void RTSPTSStreamer::stopStreaming() {
    shouldStop = 1;  // 设置为非零值来停止事件循环
    isRunning = false;

    if (sms != NULL) {
        rtspServer->removeServerMediaSession(sms);
        sms = NULL;
    }
}

std::string RTSPTSStreamer::getRTSPURL() const {
    if (rtspServer == NULL || sms == NULL) return "";

    char* url = rtspServer->rtspURL(sms);
    if (url == NULL) return "";

    std::string result(url);
    delete[] url;
    return result;
}

std::string RTSPTSStreamer::getCurrentTSFile() const {
    return currentTSFile;
}

bool RTSPTSStreamer::isFileExists(const std::string& filePath) const {
#ifdef _WIN32
    return (_access(filePath.c_str(), F_OK) == 0);
#else
    return (access(filePath.c_str(), F_OK) == 0);
#endif
}

// 全局变量用于信号处理
RTSPTSStreamer* g_streamer = nullptr;

#ifdef _WIN32
BOOL WINAPI consoleHandler(DWORD signal) {
    if (signal == CTRL_C_EVENT || signal == CTRL_CLOSE_EVENT) {
        std::cout << "\nReceived shutdown signal, stopping streamer..." << std::endl;
        if (g_streamer) {
            g_streamer->stopStreaming();
        }
        return TRUE;
    }
    return FALSE;
}
#else
void signalHandler(int signum) {
    std::cout << "\nReceived signal " << signum << ", shutting down..." << std::endl;
    if (g_streamer) {
        g_streamer->stopStreaming();
    }
    exit(signum);
}
#endif

// 业务逻辑：根据需要切换TS文件
void businessLogic(RTSPTSStreamer& streamer) {
    // 示例：每30秒切换一次文件（根据实际业务需求修改）
    std::vector<std::string> tsFiles = {
        "/home/<USER>/zeno/rtsp_ts/video.ts",
        "/home/<USER>/zeno/rtsp_ts/video2.ts",  // 如果有第二个文件
        // 可以添加更多文件路径
    };

    int currentIndex = 0;
    while (true) {
        std::this_thread::sleep_for(std::chrono::seconds(30));

        // 根据业务逻辑选择下一个文件
        currentIndex = (currentIndex + 1) % tsFiles.size();

        std::cout << "Switching to file: " << tsFiles[currentIndex] << std::endl;
        if (!streamer.setTSFile(tsFiles[currentIndex])) {
            std::cerr << "Failed to switch to file: " << tsFiles[currentIndex] << std::endl;
        }
    }
}

int main(int argc, char* argv[]) {
    // 设置信号处理
#ifdef _WIN32
    if (!SetConsoleCtrlHandler(consoleHandler, TRUE)) {
        std::cerr << "Warning: Could not set control handler" << std::endl;
    }
#else
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
#endif

    // 创建RTSP流推送器
    RTSPTSStreamer streamer(8554);
    g_streamer = &streamer;

    if (!streamer.initialize()) {
        std::cerr << "Failed to initialize RTSP streamer" << std::endl;
        return -1;
    }

    std::cout << "RTSP TS Streamer starting..." << std::endl;

    // 设置默认文件
    std::string initialFile = "/home/<USER>/zeno/rtsp_ts/video.ts";

    // 如果命令行提供了文件路径或流名称，使用命令行参数
    if (argc > 1) {
        std::string arg = argv[1];

        // 检查是否是文件路径（包含 .ts 或 /）
        if (arg.find(".ts") != std::string::npos || arg.find("/") != std::string::npos) {
            initialFile = arg;
        } else {
            // 否则认为是流名称
            if (!streamer.setTSFileByStreamName(arg)) {
                std::cerr << "Failed to set stream: " << arg << std::endl;
                std::cerr << "Available streams: video1, video2, test" << std::endl;
                return -1;
            }
        }
    }

    // 如果没有通过流名称设置，则使用文件路径
    if (streamer.getCurrentTSFile().empty()) {
        if (!streamer.setTSFile(initialFile)) {
            std::cerr << "Failed to set TS file: " << initialFile << std::endl;
            return -1;
        }
    }

    std::cout << "\nUsage examples:" << std::endl;
    std::cout << "  " << argv[0] << " video1          # Use predefined stream 'video1'" << std::endl;
    std::cout << "  " << argv[0] << " /path/to/file.ts # Use specific file" << std::endl;
    std::cout << "\nAvailable predefined streams: video1, video2, test" << std::endl;
    std::cout << "RTSP URL: rtsp://localhost:8554/stream" << std::endl;

    // 启动流推送
    streamer.startStreaming();

    // 在单独的线程中运行业务逻辑（文件切换逻辑）
    std::thread businessThread(businessLogic, std::ref(streamer));
    businessThread.detach();

    std::cout << "RTSP TS Streamer started successfully!" << std::endl;
    std::cout << "Press Ctrl+C to stop..." << std::endl;

    // 运行事件循环
    streamer.runEventLoop();

    return 0;
}



