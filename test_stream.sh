#!/bin/bash

echo "RTSP TS Streamer Test Script"
echo "============================"

# 检查 TS 文件
if [ ! -f "video.ts" ]; then
    echo "Error: video.ts file not found!"
    exit 1
fi

echo "1. Checking TS file..."
./check_ts video.ts
if [ $? -ne 0 ]; then
    echo "TS file validation failed!"
    exit 1
fi

echo ""
echo "2. Starting RTSP streamer..."
echo "   RTSP URL will be: rtsp://localhost:8554/test"
echo "   Press Ctrl+C to stop"
echo ""

# 启动流媒体服务器
./rtsp_ts_streamer

echo ""
echo "Streamer stopped."
