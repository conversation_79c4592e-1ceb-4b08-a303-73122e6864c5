#include <iostream>
#include <fstream>
#include <iomanip>

int main(int argc, char* argv[]) {
    if (argc != 2) {
        std::cout << "Usage: " << argv[0] << " <ts_file>" << std::endl;
        return 1;
    }

    std::ifstream file(argv[1], std::ios::binary);
    if (!file.is_open()) {
        std::cout << "Error: Cannot open file " << argv[1] << std::endl;
        return 1;
    }

    // TS packet size is 188 bytes
    const int TS_PACKET_SIZE = 188;
    char buffer[TS_PACKET_SIZE];
    int packetCount = 0;
    int syncErrors = 0;

    std::cout << "Checking TS file: " << argv[1] << std::endl;
    std::cout << "Expected sync byte: 0x47" << std::endl;
    std::cout << "----------------------------------------" << std::endl;

    while (file.read(buffer, TS_PACKET_SIZE)) {
        packetCount++;
        
        // Check sync byte (should be 0x47)
        unsigned char syncByte = static_cast<unsigned char>(buffer[0]);
        
        if (syncByte != 0x47) {
            syncErrors++;
            std::cout << "Packet " << packetCount << ": SYNC ERROR! Found 0x" 
                      << std::hex << std::setw(2) << std::setfill('0') 
                      << static_cast<int>(syncByte) << " instead of 0x47" << std::endl;
            
            // Show first few bytes for debugging
            std::cout << "  First 16 bytes: ";
            for (int i = 0; i < 16 && i < TS_PACKET_SIZE; i++) {
                std::cout << std::hex << std::setw(2) << std::setfill('0') 
                          << static_cast<int>(static_cast<unsigned char>(buffer[i])) << " ";
            }
            std::cout << std::endl;
        }
        
        // Only show first 10 packets and first 10 errors
        if (packetCount <= 10 || syncErrors <= 10) {
            if (syncByte == 0x47) {
                std::cout << "Packet " << packetCount << ": OK (sync byte 0x47)" << std::endl;
            }
        }
        
        if (packetCount >= 100 && syncErrors == 0) {
            std::cout << "First 100 packets look good, stopping check..." << std::endl;
            break;
        }
        
        if (syncErrors > 10) {
            std::cout << "Too many sync errors, stopping check..." << std::endl;
            break;
        }
    }

    file.close();

    std::cout << "----------------------------------------" << std::endl;
    std::cout << "Summary:" << std::endl;
    std::cout << "Total packets checked: " << packetCount << std::endl;
    std::cout << "Sync errors: " << syncErrors << std::endl;
    
    if (syncErrors == 0) {
        std::cout << "✓ TS file appears to be valid!" << std::endl;
    } else {
        std::cout << "✗ TS file has sync byte errors!" << std::endl;
    }

    return syncErrors > 0 ? 1 : 0;
}
