#include <liveMedia.hh>
#include <BasicUsageEnvironment.hh>
#include <GroupsockHelper.hh>
#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include <mutex>
#include <atomic>
#include <vector>

#ifdef _WIN32
    #include <windows.h>
    #include <io.h>
    #define sleep(x) Sleep((x)*1000)
    #define access _access
    #define F_OK 0
#else
    #include <unistd.h>
    #include <signal.h>
#endif

// 使用 Live555 内置的 MPEG2TransportFileServerMediaSubsession
// 这是专门为 TS 文件设计的，应该能正确处理同步字节

class RTSPTSStreamer {
private:
    TaskScheduler* scheduler;
    UsageEnvironment* env;
    RTSPServer* rtspServer;
    ServerMediaSession* sms;
    std::string currentTSFile;
    std::mutex fileMutex;
    std::atomic<bool> isRunning;
    std::atomic<char> shouldStop;  // live555 EventLoopWatchVariable
    int serverPort;
    std::vector<std::pair<std::string, std::string>> streamList; // (streamName, filePath)

public:
    RTSPTSStreamer(int port = 8554);
    ~RTSPTSStreamer();

    bool initialize();
    bool setTSFile(const std::string& tsFilePath);
    bool addTSFile(const std::string& tsFilePath, const std::string& streamName);
    bool addTSFilesFromDirectory(const std::string& directory);
    void startStreaming();
    void stopStreaming();
    std::string getRTSPURL() const;
    std::vector<std::string> getAvailableStreams() const;
    bool isFileExists(const std::string& filePath) const;
    void runEventLoop();
};

// 不再需要自定义实现，使用 Live555 内置的处理

RTSPTSStreamer::RTSPTSStreamer(int port) : serverPort(port), isRunning(false), shouldStop(0) {
    scheduler = BasicTaskScheduler::createNew();
    env = BasicUsageEnvironment::createNew(*scheduler);
    rtspServer = RTSPServer::createNew(*env, port, NULL);
    sms = NULL;
}

RTSPTSStreamer::~RTSPTSStreamer() {
    stopStreaming();
    Medium::close(rtspServer);
    env->reclaim();
    delete scheduler;
}

bool RTSPTSStreamer::initialize() {
    if (rtspServer == NULL) {
        *env << "Failed to create RTSP server\n";
        return false;
    }
    return true;
}

bool RTSPTSStreamer::setTSFile(const std::string& tsFilePath) {
    std::lock_guard<std::mutex> lock(fileMutex);

    // 检查文件是否存在
    if (!isFileExists(tsFilePath)) {
        std::cerr << "Error: TS file does not exist: " << tsFilePath << std::endl;
        return false;
    }

    stopStreaming();
    currentTSFile = tsFilePath;

    sms = ServerMediaSession::createNew(*env, "tsStream",
                                       "TS Stream", "Session streamed by live555");

    // 使用 Live555 内置的 MPEG2TransportFileServerMediaSubsession
    sms->addSubsession(MPEG2TransportFileServerMediaSubsession::createNew(*env,
                                                                         currentTSFile.c_str(),
                                                                         NULL,  // index file name (可选)
                                                                         False)); // 不重用第一个源
    rtspServer->addServerMediaSession(sms);

    std::cout << "Set TS file: " << currentTSFile << std::endl;
    std::cout << "RTSP URL: " << getRTSPURL() << std::endl;
    return true;
}

bool RTSPTSStreamer::addTSFile(const std::string& tsFilePath, const std::string& streamName) {
    std::lock_guard<std::mutex> lock(fileMutex);

    // 检查文件是否存在
    if (!isFileExists(tsFilePath)) {
        std::cerr << "Error: TS file does not exist: " << tsFilePath << std::endl;
        return false;
    }

    // 检查流名称是否已存在
    for (const auto& stream : streamList) {
        if (stream.first == streamName) {
            std::cerr << "Error: Stream name already exists: " << streamName << std::endl;
            return false;
        }
    }

    // 创建新的 ServerMediaSession
    ServerMediaSession* newSms = ServerMediaSession::createNew(*env, streamName.c_str(),
                                                              ("TS Stream: " + streamName).c_str(),
                                                              "Session streamed by live555");

    // 添加 TS 文件子会话
    newSms->addSubsession(MPEG2TransportFileServerMediaSubsession::createNew(*env,
                                                                            tsFilePath.c_str(),
                                                                            NULL,  // index file name
                                                                            False)); // 不重用第一个源

    // 添加到 RTSP 服务器
    rtspServer->addServerMediaSession(newSms);

    // 记录到流列表
    streamList.push_back(std::make_pair(streamName, tsFilePath));

    std::cout << "Added TS file: " << tsFilePath << " as stream: " << streamName << std::endl;
    std::cout << "RTSP URL: rtsp://localhost:" << serverPort << "/" << streamName << std::endl;

    return true;
}

bool RTSPTSStreamer::addTSFilesFromDirectory(const std::string& directory) {
    std::lock_guard<std::mutex> lock(fileMutex);

    // 这里可以实现从目录自动添加所有 TS 文件的逻辑
    // 为了简单起见，先返回 false，表示未实现
    std::cout << "addTSFilesFromDirectory not implemented yet" << std::endl;
    return false;
}

std::vector<std::string> RTSPTSStreamer::getAvailableStreams() const {
    std::vector<std::string> streams;
    for (const auto& stream : streamList) {
        streams.push_back(stream.first);
    }
    return streams;
}

void RTSPTSStreamer::startStreaming() {
    if (sms == NULL) {
        std::cout << "No TS file set. Call setTSFile() first." << std::endl;
        return;
    }

    isRunning = true;
    shouldStop = 0;
    std::cout << "Starting RTSP streaming..." << std::endl;
    std::cout << "Server listening on port " << serverPort << std::endl;
    std::cout << "RTSP URL: " << getRTSPURL() << std::endl;
}

void RTSPTSStreamer::runEventLoop() {
    if (!isRunning) {
        std::cout << "Streamer not started. Call startStreaming() first." << std::endl;
        return;
    }

    // 运行事件循环，但允许中断
    env->taskScheduler().doEventLoop(&shouldStop);
}

void RTSPTSStreamer::stopStreaming() {
    shouldStop = 1;  // 设置为非零值来停止事件循环
    isRunning = false;

    if (sms != NULL) {
        rtspServer->removeServerMediaSession(sms);
        sms = NULL;
    }
}

std::string RTSPTSStreamer::getRTSPURL() const {
    if (rtspServer == NULL || sms == NULL) return "";

    char* url = rtspServer->rtspURL(sms);
    if (url == NULL) return "";

    std::string result(url);
    delete[] url;
    return result;
}

bool RTSPTSStreamer::isFileExists(const std::string& filePath) const {
#ifdef _WIN32
    return (_access(filePath.c_str(), F_OK) == 0);
#else
    return (access(filePath.c_str(), F_OK) == 0);
#endif
}

// 全局变量用于信号处理
RTSPTSStreamer* g_streamer = nullptr;

#ifdef _WIN32
BOOL WINAPI consoleHandler(DWORD signal) {
    if (signal == CTRL_C_EVENT || signal == CTRL_CLOSE_EVENT) {
        std::cout << "\nReceived shutdown signal, stopping streamer..." << std::endl;
        if (g_streamer) {
            g_streamer->stopStreaming();
        }
        return TRUE;
    }
    return FALSE;
}
#else
void signalHandler(int signum) {
    std::cout << "\nReceived signal " << signum << ", shutting down..." << std::endl;
    if (g_streamer) {
        g_streamer->stopStreaming();
    }
    exit(signum);
}
#endif

// 业务逻辑：根据需要切换TS文件
void businessLogic(RTSPTSStreamer& streamer) {
    // 示例：每30秒切换一次文件（根据实际业务需求修改）
    std::vector<std::string> tsFiles = {
        "/home/<USER>/zeno/rtsp_ts/video.ts",
        "/home/<USER>/zeno/rtsp_ts/video2.ts",  // 如果有第二个文件
        // 可以添加更多文件路径
    };

    int currentIndex = 0;
    while (true) {
        std::this_thread::sleep_for(std::chrono::seconds(30));

        // 根据业务逻辑选择下一个文件
        currentIndex = (currentIndex + 1) % tsFiles.size();

        std::cout << "Switching to file: " << tsFiles[currentIndex] << std::endl;
        if (!streamer.setTSFile(tsFiles[currentIndex])) {
            std::cerr << "Failed to switch to file: " << tsFiles[currentIndex] << std::endl;
        }
    }
}

int main(int argc, char* argv[]) {
    // 设置信号处理
#ifdef _WIN32
    if (!SetConsoleCtrlHandler(consoleHandler, TRUE)) {
        std::cerr << "Warning: Could not set control handler" << std::endl;
    }
#else
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
#endif

    // 创建RTSP流推送器
    RTSPTSStreamer streamer(8554);
    g_streamer = &streamer;

    if (!streamer.initialize()) {
        std::cerr << "Failed to initialize RTSP streamer" << std::endl;
        return -1;
    }

    std::cout << "RTSP TS Streamer starting..." << std::endl;

    // 添加多个 TS 文件流
    std::vector<std::pair<std::string, std::string>> defaultFiles = {
        {"video1", "/home/<USER>/zeno/rtsp_ts/video.ts"},
        {"video2", "/home/<USER>/zeno/rtsp_ts/video2.ts"},
        {"test", "/home/<USER>/zeno/rtsp_ts/test.ts"}
    };

    // 如果命令行提供了文件路径，只添加该文件
    if (argc > 1) {
        std::string filePath = argv[1];
        std::string streamName = "stream1";
        if (argc > 2) {
            streamName = argv[2];
        }

        if (!streamer.addTSFile(filePath, streamName)) {
            std::cerr << "Failed to add TS file: " << filePath << std::endl;
            return -1;
        }
    } else {
        // 添加默认文件（如果存在）
        bool addedAny = false;
        for (const auto& file : defaultFiles) {
            if (streamer.isFileExists(file.second)) {
                if (streamer.addTSFile(file.second, file.first)) {
                    addedAny = true;
                }
            } else {
                std::cout << "File not found, skipping: " << file.second << std::endl;
            }
        }

        if (!addedAny) {
            std::cerr << "No valid TS files found!" << std::endl;
            return -1;
        }
    }

    // 显示所有可用的流
    auto streams = streamer.getAvailableStreams();
    std::cout << "\nAvailable streams:" << std::endl;
    for (const auto& stream : streams) {
        std::cout << "  rtsp://localhost:8554/" << stream << std::endl;
    }

    // 启动流推送
    streamer.startStreaming();

    // 在单独的线程中运行业务逻辑（文件切换逻辑）
    std::thread businessThread(businessLogic, std::ref(streamer));
    businessThread.detach();

    std::cout << "RTSP TS Streamer started successfully!" << std::endl;
    std::cout << "Press Ctrl+C to stop..." << std::endl;

    // 运行事件循环
    streamer.runEventLoop();

    return 0;
}



